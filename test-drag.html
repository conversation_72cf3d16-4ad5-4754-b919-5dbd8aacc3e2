<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Accessibility Menu Drag</title>
    <link rel="stylesheet" href="assets/css/accessibility.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            min-height: 100vh;
        }
        .test-content {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="test-content">
        <h1>Test Accessibility Menu Drag Functionality</h1>
        <p>This page tests the draggable accessibility menu. The menu should appear in the bottom-left corner.</p>
        <p>Once you click the accessibility button (♿), the menu should open and you should be able to drag it using the handle at the top.</p>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Mock WordPress localization
        window.easOptions = {
            ajaxUrl: '',
            nonce: 'test-nonce',
            options: {
                enable_text_to_speech: true,
                enable_background_toggle: true,
                enable_font_size_controls: true,
                enable_contrast_controls: true,
                enable_focus_indicators: true,
                enable_keyboard_navigation: true,
                button_position: 'bottom-left',
                speech_rate: 1.0,
                speech_pitch: 1.0,
                speech_volume: 1.0,
                default_font_size: 16,
                max_font_size: 24,
                min_font_size: 12
            },
            strings: {
                toggleBackground: 'Toggle Background',
                increaseFontSize: 'Increase Font Size',
                decreaseFontSize: 'Decrease Font Size',
                resetFontSize: 'Reset Font Size',
                highContrast: 'High Contrast',
                normalContrast: 'Normal Contrast',
                readAloud: 'Read Aloud',
                stopReading: 'Stop Reading',
                accessibilityMenu: 'Accessibility Menu',
                closeMenu: 'Close Menu'
            }
        };


    </script>
    <script src="assets/js/accessibility.js"></script>
</body>
</html>
