<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Drag Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .test-panel {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            min-width: 200px;
            z-index: 999999;
        }
        
        .drag-handle {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            border-radius: 8px 8px 0 0;
            cursor: move;
            font-size: 16px;
            color: #666;
            min-height: 20px;
        }
        
        .drag-handle::before {
            content: "⋮⋮";
            font-weight: bold;
            letter-spacing: 2px;
            transform: rotate(90deg);
        }
        
        .drag-handle:hover {
            background: #e9ecef;
            color: #333;
        }
        
        .menu-items {
            padding: 8px 0;
        }
        
        .menu-item {
            display: block;
            width: 100%;
            padding: 12px 16px;
            background: none;
            border: none;
            text-align: left;
            font-size: 14px;
            color: #333;
            cursor: pointer;
        }
        
        .menu-item:hover {
            background-color: #f5f5f5;
        }
        
        .dragging {
            user-select: none;
        }
        
        .dragging .menu-items {
            pointer-events: none;
        }
    </style>
</head>
<body>
    <h1>Simple Drag Test</h1>
    <p>This tests a simple draggable panel with a drag handle.</p>
    
    <div class="test-panel" id="testPanel">
        <div class="drag-handle" id="dragHandle">
        </div>
        <div class="menu-items">
            <button class="menu-item">Test Item 1</button>
            <button class="menu-item">Test Item 2</button>
            <button class="menu-item">Test Item 3</button>
        </div>
    </div>

    <script>
        class SimpleDrag {
            constructor() {
                this.panel = document.getElementById('testPanel');
                this.dragHandle = document.getElementById('dragHandle');
                this.isDragging = false;
                this.dragOffset = { x: 0, y: 0 };
                
                this.init();
            }
            
            init() {
                this.dragHandle.addEventListener('mousedown', (e) => {
                    if (e.button !== 0) return;
                    this.startDrag(e);
                });
                
                document.addEventListener('mousemove', (e) => {
                    if (this.isDragging) {
                        this.handleDrag(e);
                    }
                });
                
                document.addEventListener('mouseup', () => {
                    if (this.isDragging) {
                        this.endDrag();
                    }
                });
            }
            
            startDrag(e) {
                this.isDragging = true;
                this.panel.classList.add('dragging');
                
                const rect = this.panel.getBoundingClientRect();
                this.dragOffset = {
                    x: e.clientX - rect.left,
                    y: e.clientY - rect.top
                };
                
                console.log('Drag started', this.dragOffset);
            }
            
            handleDrag(e) {
                const newX = e.clientX - this.dragOffset.x;
                const newY = e.clientY - this.dragOffset.y;
                
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;
                const panelRect = this.panel.getBoundingClientRect();
                
                const constrainedX = Math.max(0, Math.min(newX, viewportWidth - panelRect.width));
                const constrainedY = Math.max(0, Math.min(newY, viewportHeight - panelRect.height));
                
                this.panel.style.left = constrainedX + 'px';
                this.panel.style.top = constrainedY + 'px';
            }
            
            endDrag() {
                this.isDragging = false;
                this.panel.classList.remove('dragging');
                console.log('Drag ended');
            }
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new SimpleDrag();
        });
    </script>
</body>
</html>
