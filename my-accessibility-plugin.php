<?php
/**
 * Plugin Name: Enhanced Accessibility Suite
 * Plugin URI: https://your-website.com/enhanced-accessibility-suite
 * Description: A comprehensive accessibility enhancement plugin featuring text-to-speech, visual adjustments, keyboard navigation, and customizable accessibility tools for WordPress websites.
 * Version: 2.0.0
 * Author: <PERSON><PERSON> el aabidine
 * Author URI: https://your-website.com
 * Text Domain: enhanced-accessibility-suite
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * License: GPL
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Network: false
 *
 * Enhanced Accessibility Suite is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * any later version.
 *
 * Enhanced Accessibility Suite is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Enhanced Accessibility Suite. If not, see https://www.gnu.org/licenses/gpl-2.0.html.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('EAS_VERSION', '2.0.0');
define('EAS_PLUGIN_FILE', __FILE__);
define('EAS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('EAS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('EAS_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('EAS_TEXT_DOMAIN', 'enhanced-accessibility-suite');

/**
 * Main Enhanced Accessibility Suite Plugin Class
 *
 * This class handles the initialization and core functionality of the
 * Enhanced Accessibility Suite plugin.
 *
 * @since 2.0.0
 * @package Enhanced_Accessibility_Suite
 */
class Enhanced_Accessibility_Suite {

    /**
     * Plugin instance
     *
     * @since 2.0.0
     * @var Enhanced_Accessibility_Suite
     */
    private static $instance = null;

    /**
     * Plugin options
     *
     * @since 2.0.0
     * @var array
     */
    private $options;

    /**
     * Default plugin options
     *
     * @since 2.0.0
     * @var array
     */
    private $default_options = array(
        'enable_text_to_speech' => true,
        'enable_background_toggle' => true,
        'enable_font_size_controls' => true,
        'enable_contrast_controls' => true,
        'enable_focus_indicators' => true,
        'enable_keyboard_navigation' => true,
        'button_position' => 'bottom-left',
        'speech_rate' => 1.0,
        'speech_pitch' => 1.0,
        'speech_volume' => 1.0,
        'default_font_size' => 16,
        'max_font_size' => 24,
        'min_font_size' => 12,
        // Premium features
        'enable_custom_colors' => true,
        'enable_animations' => true,
        'enable_reading_guide' => true,
        'enable_dyslexia_font' => true,
        'custom_primary_color' => '#0073aa',
        'custom_secondary_color' => '#005a87',
        'custom_accent_color' => '#ffb900',
        'animation_speed' => 'normal',
        'reading_guide_color' => '#ff6b6b',
        'reading_guide_height' => 3,
        'speech_presets' => array(
            'slow' => array('rate' => 0.7, 'pitch' => 1.0, 'volume' => 1.0),
            'normal' => array('rate' => 1.0, 'pitch' => 1.0, 'volume' => 1.0),
            'fast' => array('rate' => 1.3, 'pitch' => 1.0, 'volume' => 1.0),
        ),
        'enable_usage_analytics' => false,
        'enable_accessibility_report' => true,
    );

    /**
     * Get plugin instance
     *
     * @since 2.0.0
     * @return Enhanced_Accessibility_Suite
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     *
     * @since 2.0.0
     */
    private function __construct() {
        $this->load_options();
        $this->init_hooks();
    }

    /**
     * Load plugin options
     *
     * @since 2.0.0
     */
    private function load_options() {
        $this->options = wp_parse_args(
            get_option('eas_options', array()),
            $this->default_options
        );
    }

    /**
     * Initialize WordPress hooks
     *
     * @since 2.0.0
     */
    private function init_hooks() {
        // Core hooks
        add_action('init', array($this, 'load_textdomain'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));

        // Admin hooks
        if (is_admin()) {
            add_action('admin_menu', array($this, 'add_admin_menu'));
            add_action('admin_init', array($this, 'register_settings'));
        }

        // AJAX hooks
        add_action('wp_ajax_eas_save_settings', array($this, 'ajax_save_settings'));
        add_action('wp_ajax_eas_reset_settings', array($this, 'ajax_reset_settings'));

        // Activation and deactivation hooks
        register_activation_hook(EAS_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(EAS_PLUGIN_FILE, array($this, 'deactivate'));
    }

    /**
     * Load plugin text domain for internationalization
     *
     * @since 2.0.0
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            EAS_TEXT_DOMAIN,
            false,
            dirname(EAS_PLUGIN_BASENAME) . '/languages'
        );
    }
    /**
     * Enqueue frontend assets
     *
     * @since 2.0.0
     */
    public function enqueue_frontend_assets() {
        // Only load on front-end
        if (is_admin()) {
            return;
        }

        // Enqueue CSS
        wp_enqueue_style(
            'eas-frontend-css',
            EAS_PLUGIN_URL . 'assets/css/accessibility.css',
            array(),
            EAS_VERSION
        );

        // Enqueue premium CSS if features are enabled
        if ($this->has_premium_features()) {
            wp_enqueue_style(
                'eas-premium-css',
                EAS_PLUGIN_URL . 'assets/css/premium-features.css',
                array('eas-frontend-css'),
                EAS_VERSION
            );
        }

        // Enqueue JavaScript
        wp_enqueue_script(
            'eas-frontend-js',
            EAS_PLUGIN_URL . 'assets/js/accessibility.js',
            array('jquery'),
            EAS_VERSION,
            true
        );

        // Enqueue premium JavaScript if features are enabled
        if ($this->has_premium_features()) {
            wp_enqueue_script(
                'eas-premium-js',
                EAS_PLUGIN_URL . 'assets/js/premium-features.js',
                array('eas-frontend-js'),
                EAS_VERSION,
                true
            );
        }

        // Localize script with options and translations
        wp_localize_script('eas-frontend-js', 'easOptions', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('eas_nonce'),
            'options' => $this->options,
            'strings' => array(
                'toggleBackground' => __('Toggle Background', EAS_TEXT_DOMAIN),
                'increaseFontSize' => __('Increase Font Size', EAS_TEXT_DOMAIN),
                'decreaseFontSize' => __('Decrease Font Size', EAS_TEXT_DOMAIN),
                'resetFontSize' => __('Reset Font Size', EAS_TEXT_DOMAIN),
                'highContrast' => __('High Contrast', EAS_TEXT_DOMAIN),
                'normalContrast' => __('Normal Contrast', EAS_TEXT_DOMAIN),
                'readAloud' => __('Read Aloud', EAS_TEXT_DOMAIN),
                'stopReading' => __('Stop Reading', EAS_TEXT_DOMAIN),
                'accessibilityMenu' => __('Accessibility Menu', EAS_TEXT_DOMAIN),
                'closeMenu' => __('Close Menu', EAS_TEXT_DOMAIN),
            )
        ));
    }

    /**
     * Enqueue admin assets
     *
     * @since 2.0.0
     * @param string $hook_suffix The current admin page
     */
    public function enqueue_admin_assets($hook_suffix) {
        // Only load on plugin settings page
        if ('settings_page_enhanced-accessibility-suite' !== $hook_suffix) {
            return;
        }

        wp_enqueue_style(
            'eas-admin-css',
            EAS_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            EAS_VERSION
        );

        wp_enqueue_script(
            'eas-admin-js',
            EAS_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            EAS_VERSION,
            true
        );

        wp_localize_script('eas-admin-js', 'easAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('eas_admin_nonce'),
            'strings' => array(
                'settingsSaved' => __('Settings saved successfully!', EAS_TEXT_DOMAIN),
                'settingsReset' => __('Settings reset to defaults!', EAS_TEXT_DOMAIN),
                'error' => __('An error occurred. Please try again.', EAS_TEXT_DOMAIN),
                'confirmReset' => __('Are you sure you want to reset all settings to defaults?', EAS_TEXT_DOMAIN),
            )
        ));
    }

    /**
     * Add admin menu
     *
     * @since 2.0.0
     */
    public function add_admin_menu() {
        add_options_page(
            __('Enhanced Accessibility Suite', EAS_TEXT_DOMAIN),
            __('Accessibility Suite', EAS_TEXT_DOMAIN),
            'manage_options',
            'enhanced-accessibility-suite',
            array($this, 'admin_page')
        );
    }

    /**
     * Register plugin settings
     *
     * @since 2.0.0
     */
    public function register_settings() {
        register_setting(
            'eas_options_group',
            'eas_options',
            array($this, 'sanitize_options')
        );
    }

    /**
     * Sanitize plugin options
     *
     * @since 2.0.0
     * @param array $input Raw input data
     * @return array Sanitized options
     */
    public function sanitize_options($input) {
        $sanitized = array();

        // Boolean options
        $boolean_options = array(
            'enable_text_to_speech',
            'enable_background_toggle',
            'enable_font_size_controls',
            'enable_contrast_controls',
            'enable_focus_indicators',
            'enable_keyboard_navigation',
            'enable_custom_colors',
            'enable_animations',
            'enable_reading_guide',
            'enable_dyslexia_font',
            'enable_usage_analytics',
            'enable_accessibility_report'
        );

        foreach ($boolean_options as $option) {
            $sanitized[$option] = isset($input[$option]) ? (bool) $input[$option] : false;
        }

        // String options
        $sanitized['button_position'] = isset($input['button_position']) ?
            sanitize_text_field($input['button_position']) : 'bottom-left';
        $sanitized['animation_speed'] = isset($input['animation_speed']) ?
            sanitize_text_field($input['animation_speed']) : 'normal';

        // Color options
        $sanitized['custom_primary_color'] = isset($input['custom_primary_color']) ?
            sanitize_hex_color($input['custom_primary_color']) : '#0073aa';
        $sanitized['custom_secondary_color'] = isset($input['custom_secondary_color']) ?
            sanitize_hex_color($input['custom_secondary_color']) : '#005a87';
        $sanitized['custom_accent_color'] = isset($input['custom_accent_color']) ?
            sanitize_hex_color($input['custom_accent_color']) : '#ffb900';
        $sanitized['reading_guide_color'] = isset($input['reading_guide_color']) ?
            sanitize_hex_color($input['reading_guide_color']) : '#ff6b6b';

        // Float options
        $sanitized['speech_rate'] = isset($input['speech_rate']) ?
            (float) $input['speech_rate'] : 1.0;
        $sanitized['speech_pitch'] = isset($input['speech_pitch']) ?
            (float) $input['speech_pitch'] : 1.0;
        $sanitized['speech_volume'] = isset($input['speech_volume']) ?
            (float) $input['speech_volume'] : 1.0;

        // Integer options
        $sanitized['default_font_size'] = isset($input['default_font_size']) ?
            absint($input['default_font_size']) : 16;
        $sanitized['max_font_size'] = isset($input['max_font_size']) ?
            absint($input['max_font_size']) : 24;
        $sanitized['min_font_size'] = isset($input['min_font_size']) ?
            absint($input['min_font_size']) : 12;
        $sanitized['reading_guide_height'] = isset($input['reading_guide_height']) ?
            absint($input['reading_guide_height']) : 3;

        // Array options (speech presets)
        if (isset($input['speech_presets']) && is_array($input['speech_presets'])) {
            $sanitized['speech_presets'] = array();
            foreach ($input['speech_presets'] as $preset_name => $preset_values) {
                if (is_array($preset_values)) {
                    $sanitized['speech_presets'][sanitize_key($preset_name)] = array(
                        'rate' => isset($preset_values['rate']) ? (float) $preset_values['rate'] : 1.0,
                        'pitch' => isset($preset_values['pitch']) ? (float) $preset_values['pitch'] : 1.0,
                        'volume' => isset($preset_values['volume']) ? (float) $preset_values['volume'] : 1.0,
                    );
                }
            }
        } else {
            $sanitized['speech_presets'] = $this->default_options['speech_presets'];
        }

        // Validate ranges
        $sanitized['speech_rate'] = max(0.1, min(3.0, $sanitized['speech_rate']));
        $sanitized['speech_pitch'] = max(0.1, min(2.0, $sanitized['speech_pitch']));
        $sanitized['speech_volume'] = max(0.1, min(1.0, $sanitized['speech_volume']));
        $sanitized['default_font_size'] = max(10, min(30, $sanitized['default_font_size']));
        $sanitized['max_font_size'] = max(16, min(40, $sanitized['max_font_size']));
        $sanitized['min_font_size'] = max(8, min(20, $sanitized['min_font_size']));
        $sanitized['reading_guide_height'] = max(1, min(10, $sanitized['reading_guide_height']));

        // Validate animation speed
        $valid_speeds = array('slow', 'normal', 'fast', 'none');
        if (!in_array($sanitized['animation_speed'], $valid_speeds)) {
            $sanitized['animation_speed'] = 'normal';
        }

        // Validate speech preset values
        foreach ($sanitized['speech_presets'] as $preset_name => $preset_values) {
            $sanitized['speech_presets'][$preset_name]['rate'] = max(0.1, min(3.0, $preset_values['rate']));
            $sanitized['speech_presets'][$preset_name]['pitch'] = max(0.1, min(2.0, $preset_values['pitch']));
            $sanitized['speech_presets'][$preset_name]['volume'] = max(0.1, min(1.0, $preset_values['volume']));
        }

        return $sanitized;
    }
    /**
     * AJAX handler for saving settings
     *
     * @since 2.0.0
     */
    public function ajax_save_settings() {
        // Verify nonce and capabilities
        if (!wp_verify_nonce($_POST['nonce'], 'eas_admin_nonce') ||
            !current_user_can('manage_options')) {
            wp_die(__('Security check failed.', EAS_TEXT_DOMAIN));
        }

        $options = isset($_POST['options']) ? $_POST['options'] : array();
        $sanitized_options = $this->sanitize_options($options);

        update_option('eas_options', $sanitized_options);
        $this->options = $sanitized_options;

        wp_send_json_success(array(
            'message' => __('Settings saved successfully!', EAS_TEXT_DOMAIN)
        ));
    }

    /**
     * AJAX handler for resetting settings
     *
     * @since 2.0.0
     */
    public function ajax_reset_settings() {
        // Verify nonce and capabilities
        if (!wp_verify_nonce($_POST['nonce'], 'eas_admin_nonce') ||
            !current_user_can('manage_options')) {
            wp_die(__('Security check failed.', EAS_TEXT_DOMAIN));
        }

        update_option('eas_options', $this->default_options);
        $this->options = $this->default_options;

        wp_send_json_success(array(
            'message' => __('Settings reset to defaults!', EAS_TEXT_DOMAIN),
            'options' => $this->default_options
        ));
    }

    /**
     * Display admin page
     *
     * @since 2.0.0
     */
    public function admin_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', EAS_TEXT_DOMAIN));
        }

        include EAS_PLUGIN_DIR . 'includes/admin-page.php';
    }

    /**
     * Plugin activation
     *
     * @since 2.0.0
     */
    public function activate() {
        // Set default options
        if (!get_option('eas_options')) {
            add_option('eas_options', $this->default_options);
        }

        // Set activation flag for welcome message
        set_transient('eas_activation_notice', true, 30);

        // Clear any cached data
        wp_cache_flush();
    }

    /**
     * Plugin deactivation
     *
     * @since 2.0.0
     */
    public function deactivate() {
        // Clear any cached data
        wp_cache_flush();

        // Remove transients
        delete_transient('eas_activation_notice');
    }

    /**
     * Get plugin options
     *
     * @since 2.0.0
     * @param string $key Optional. Specific option key to retrieve
     * @return mixed Plugin options or specific option value
     */
    public function get_option($key = null) {
        if ($key) {
            return isset($this->options[$key]) ? $this->options[$key] : null;
        }
        return $this->options;
    }

    /**
     * Update plugin option
     *
     * @since 2.0.0
     * @param string $key Option key
     * @param mixed $value Option value
     * @return bool True on success, false on failure
     */
    public function update_option($key, $value) {
        $this->options[$key] = $value;
        return update_option('eas_options', $this->options);
    }

    /**
     * Check if any premium features are enabled
     *
     * @since 2.0.0
     * @return bool True if premium features are enabled
     */
    private function has_premium_features() {
        $premium_features = array(
            'enable_custom_colors',
            'enable_animations',
            'enable_reading_guide',
            'enable_dyslexia_font',
            'enable_usage_analytics',
            'enable_accessibility_report'
        );

        foreach ($premium_features as $feature) {
            if (!empty($this->options[$feature])) {
                return true;
            }
        }

        return false;
    }
}

// Uninstall hook
if (!function_exists('eas_uninstall')) {
    /**
     * Plugin uninstall function
     *
     * @since 2.0.0
     */
    function eas_uninstall() {
        // Remove plugin options
        delete_option('eas_options');

        // Remove transients
        delete_transient('eas_activation_notice');

        // Clear any cached data
        wp_cache_flush();
    }
}
register_uninstall_hook(EAS_PLUGIN_FILE, 'eas_uninstall');

// Initialize the plugin
function eas_init() {
    return Enhanced_Accessibility_Suite::get_instance();
}

// Start the plugin
add_action('plugins_loaded', 'eas_init');

// Convenience function to get plugin instance
function eas() {
    return Enhanced_Accessibility_Suite::get_instance();
}