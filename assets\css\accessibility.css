/**
 * Enhanced Accessibility Suite - Frontend Styles
 *
 * @since 2.0.0
 * @package Enhanced_Accessibility_Suite
 */

/* ==========================================================================
   Accessibility Menu
   ========================================================================== */

.eas-accessibility-menu {
    position: fixed;
    z-index: 999999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Menu positioning */
.eas-position-top-left {
    top: 20px;
    left: 20px;
}

.eas-position-top-right {
    top: 20px;
    right: 20px;
}

.eas-position-bottom-left {
    bottom: 20px;
    left: 20px;
}

.eas-position-bottom-right {
    bottom: 20px;
    right: 20px;
}

/* Menu toggle button */
.eas-menu-toggle {
    background: #0073aa;
    color: white;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.eas-menu-toggle:hover {
    background: #005a87;
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.eas-menu-toggle:focus {
    outline: 3px solid #ffb900;
    outline-offset: 2px;
}

.eas-menu-toggle:active {
    transform: scale(0.95);
}

/* Menu panel */
.eas-menu-panel {
    display: none;
    position: absolute;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    padding: 0;
    margin-top: 8px;
}

/* Draggable menu panel */
.eas-menu-panel.eas-draggable {
    position: fixed;
    cursor: default;
    z-index: 1000000;
}

.eas-menu-panel.eas-dragging {
    user-select: none;
    pointer-events: none;
}

.eas-menu-panel.eas-dragging * {
    pointer-events: none;
}

/* Drag handle */
.eas-drag-handle {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
    border-radius: 8px 8px 0 0;
    cursor: move;
    font-size: 16px;
    color: #666;
    min-height: 20px;
}

.eas-drag-handle:hover {
    background: #e9ecef;
    color: #333;
}

.eas-drag-handle::before {
    content: "⋮⋮";
    font-weight: bold;
    letter-spacing: 2px;
    transform: rotate(90deg);
}

.eas-drag-handle:focus {
    outline: 2px solid #0073aa;
    outline-offset: -2px;
    background: #e9ecef;
}

.eas-drag-handle:active {
    background: #dee2e6;
}

/* Menu items container */
.eas-menu-items {
    padding: 8px 0;
}

.eas-position-top-left .eas-menu-panel,
.eas-position-top-right .eas-menu-panel {
    top: 100%;
}

.eas-position-bottom-left .eas-menu-panel,
.eas-position-bottom-right .eas-menu-panel {
    bottom: 100%;
    margin-top: 0;
    margin-bottom: 8px;
}

.eas-position-top-right .eas-menu-panel,
.eas-position-bottom-right .eas-menu-panel {
    right: 0;
}

/* Menu items */
.eas-menu-item {
    display: block;
    width: 100%;
    padding: 12px 16px;
    background: none;
    border: none;
    text-align: left;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.eas-menu-item:hover {
    background-color: #f5f5f5;
}

.eas-menu-item:focus {
    background-color: #0073aa;
    color: white;
    outline: none;
}

/* ==========================================================================
   Skip Links
   ========================================================================== */

.eas-skip-links {
    position: absolute;
    top: -40px;
    left: 6px;
    z-index: 1000000;
}

.eas-skip-link {
    position: absolute;
    left: -10000px;
    top: auto;
    width: 1px;
    height: 1px;
    overflow: hidden;
    background: #0073aa;
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 0 0 4px 4px;
    font-size: 14px;
    font-weight: 600;
}

.eas-skip-link:focus {
    position: static;
    width: auto;
    height: auto;
    left: auto;
    top: auto;
    overflow: visible;
    outline: 3px solid #ffb900;
    outline-offset: 2px;
}

/* ==========================================================================
   Text-to-Speech
   ========================================================================== */

.eas-readable {
    cursor: pointer;
    transition: background-color 0.2s ease, outline 0.2s ease;
    border-radius: 2px;
}

.eas-readable:hover {
    background-color: rgba(0, 115, 170, 0.1);
}

.eas-readable:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

.eas-reading {
    background-color: rgba(255, 185, 0, 0.2) !important;
    animation: eas-pulse 1.5s infinite;
}

@keyframes eas-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}
/* ==========================================================================
   Dark Mode
   ========================================================================== */

.eas-dark-mode {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
}

.eas-dark-mode .eas-menu-panel {
    background: #2d2d2d;
    border-color: #444;
    color: #ffffff;
}

.eas-dark-mode .eas-drag-handle {
    background: #404040;
    border-bottom-color: #555;
    color: #ccc;
}

.eas-dark-mode .eas-drag-handle:hover {
    background: #4a4a4a;
    color: #fff;
}

.eas-dark-mode .eas-drag-handle:focus {
    outline: 2px solid #4a9eff;
    background: #4a4a4a;
}

.eas-dark-mode .eas-drag-handle:active {
    background: #555;
}

.eas-dark-mode .eas-menu-item {
    color: #ffffff;
}

.eas-dark-mode .eas-menu-item:hover {
    background-color: #404040;
}

.eas-dark-mode .eas-menu-item:focus {
    background-color: #0073aa;
}

.eas-dark-mode a {
    color: #66b3ff;
}

.eas-dark-mode a:visited {
    color: #b366ff;
}

.eas-dark-mode input,
.eas-dark-mode textarea,
.eas-dark-mode select {
    background-color: #2d2d2d;
    color: #ffffff;
    border-color: #555;
}

.eas-dark-mode button {
    background-color: #404040;
    color: #ffffff;
    border-color: #555;
}

/* ==========================================================================
   High Contrast Mode
   ========================================================================== */

.eas-high-contrast {
    background-color: #000000 !important;
    color: #ffffff !important;
}

.eas-high-contrast * {
    background-color: transparent !important;
    color: #ffffff !important;
    border-color: #ffffff !important;
}

.eas-high-contrast a,
.eas-high-contrast button,
.eas-high-contrast input[type="submit"],
.eas-high-contrast input[type="button"] {
    background-color: #000000 !important;
    color: #ffff00 !important;
    border: 2px solid #ffffff !important;
    text-decoration: underline !important;
}

.eas-high-contrast a:hover,
.eas-high-contrast button:hover,
.eas-high-contrast input[type="submit"]:hover,
.eas-high-contrast input[type="button"]:hover {
    background-color: #ffff00 !important;
    color: #000000 !important;
}

.eas-high-contrast input,
.eas-high-contrast textarea,
.eas-high-contrast select {
    background-color: #ffffff !important;
    color: #000000 !important;
    border: 2px solid #ffffff !important;
}

.eas-high-contrast .eas-menu-panel {
    background-color: #000000 !important;
    border: 2px solid #ffffff !important;
}

.eas-high-contrast .eas-menu-toggle {
    background-color: #000000 !important;
    color: #ffff00 !important;
    border: 2px solid #ffffff !important;
}

/* ==========================================================================
   Enhanced Focus Indicators
   ========================================================================== */

.eas-enhanced-focus *:focus {
    outline: 3px solid #ffb900 !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 5px rgba(255, 185, 0, 0.3) !important;
}

.eas-enhanced-focus a:focus,
.eas-enhanced-focus button:focus,
.eas-enhanced-focus input:focus,
.eas-enhanced-focus textarea:focus,
.eas-enhanced-focus select:focus {
    background-color: rgba(255, 185, 0, 0.1) !important;
}

/* ==========================================================================
   Font Size Adjustments
   ========================================================================== */

.eas-font-size-adjusted {
    font-size: inherit !important;
    line-height: 1.5 !important;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .eas-accessibility-menu {
        position: fixed;
    }

    .eas-position-top-left,
    .eas-position-bottom-left {
        left: 10px;
    }

    .eas-position-top-right,
    .eas-position-bottom-right {
        right: 10px;
    }

    .eas-position-top-left,
    .eas-position-top-right {
        top: 10px;
    }

    .eas-position-bottom-left,
    .eas-position-bottom-right {
        bottom: 10px;
    }

    .eas-menu-toggle {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .eas-menu-panel {
        min-width: 180px;
        max-width: calc(100vw - 40px);
    }

    .eas-drag-handle {
        padding: 6px;
        font-size: 14px;
    }

    .eas-skip-links {
        left: 4px;
    }
}

@media (max-width: 480px) {
    .eas-menu-panel {
        min-width: 160px;
        max-width: calc(100vw - 20px);
    }

    .eas-menu-item {
        padding: 10px 12px;
        font-size: 13px;
    }
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .eas-accessibility-menu,
    .eas-skip-links {
        display: none !important;
    }
}

/* ==========================================================================
   Reduced Motion
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
    .eas-menu-toggle,
    .eas-menu-item,
    .eas-readable,
    .eas-reading {
        transition: none !important;
        animation: none !important;
    }
}

/* ==========================================================================
   High Contrast Media Query
   ========================================================================== */

@media (prefers-contrast: high) {
    .eas-menu-toggle {
        border: 2px solid currentColor;
    }

    .eas-menu-panel {
        border: 2px solid currentColor;
    }

    .eas-menu-item:focus {
        outline: 2px solid currentColor;
        outline-offset: -2px;
    }
}